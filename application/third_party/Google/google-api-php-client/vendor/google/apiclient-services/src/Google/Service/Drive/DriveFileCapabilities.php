<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

class Google_Service_Drive_DriveFileCapabilities extends Google_Model
{
  public $canAddChildren;
  public $canChangeViewersCanCopyContent;
  public $canComment;
  public $canCopy;
  public $canDelete;
  public $canDownload;
  public $canEdit;
  public $canListChildren;
  public $canMoveItemIntoTeamDrive;
  public $canMoveTeamDriveItem;
  public $canReadRevisions;
  public $canReadTeamDrive;
  public $canRemoveChildren;
  public $canRename;
  public $canShare;
  public $canTrash;
  public $canUntrash;

  public function setCanAddChildren($canAddChildren)
  {
    $this->canAddChildren = $canAddChildren;
  }
  public function getCanAddChildren()
  {
    return $this->canAddChildren;
  }
  public function setCanChangeViewersCanCopyContent($canChangeViewersCanCopyContent)
  {
    $this->canChangeViewersCanCopyContent = $canChangeViewersCanCopyContent;
  }
  public function getCanChangeViewersCanCopyContent()
  {
    return $this->canChangeViewersCanCopyContent;
  }
  public function setCanComment($canComment)
  {
    $this->canComment = $canComment;
  }
  public function getCanComment()
  {
    return $this->canComment;
  }
  public function setCanCopy($canCopy)
  {
    $this->canCopy = $canCopy;
  }
  public function getCanCopy()
  {
    return $this->canCopy;
  }
  public function setCanDelete($canDelete)
  {
    $this->canDelete = $canDelete;
  }
  public function getCanDelete()
  {
    return $this->canDelete;
  }
  public function setCanDownload($canDownload)
  {
    $this->canDownload = $canDownload;
  }
  public function getCanDownload()
  {
    return $this->canDownload;
  }
  public function setCanEdit($canEdit)
  {
    $this->canEdit = $canEdit;
  }
  public function getCanEdit()
  {
    return $this->canEdit;
  }
  public function setCanListChildren($canListChildren)
  {
    $this->canListChildren = $canListChildren;
  }
  public function getCanListChildren()
  {
    return $this->canListChildren;
  }
  public function setCanMoveItemIntoTeamDrive($canMoveItemIntoTeamDrive)
  {
    $this->canMoveItemIntoTeamDrive = $canMoveItemIntoTeamDrive;
  }
  public function getCanMoveItemIntoTeamDrive()
  {
    return $this->canMoveItemIntoTeamDrive;
  }
  public function setCanMoveTeamDriveItem($canMoveTeamDriveItem)
  {
    $this->canMoveTeamDriveItem = $canMoveTeamDriveItem;
  }
  public function getCanMoveTeamDriveItem()
  {
    return $this->canMoveTeamDriveItem;
  }
  public function setCanReadRevisions($canReadRevisions)
  {
    $this->canReadRevisions = $canReadRevisions;
  }
  public function getCanReadRevisions()
  {
    return $this->canReadRevisions;
  }
  public function setCanReadTeamDrive($canReadTeamDrive)
  {
    $this->canReadTeamDrive = $canReadTeamDrive;
  }
  public function getCanReadTeamDrive()
  {
    return $this->canReadTeamDrive;
  }
  public function setCanRemoveChildren($canRemoveChildren)
  {
    $this->canRemoveChildren = $canRemoveChildren;
  }
  public function getCanRemoveChildren()
  {
    return $this->canRemoveChildren;
  }
  public function setCanRename($canRename)
  {
    $this->canRename = $canRename;
  }
  public function getCanRename()
  {
    return $this->canRename;
  }
  public function setCanShare($canShare)
  {
    $this->canShare = $canShare;
  }
  public function getCanShare()
  {
    return $this->canShare;
  }
  public function setCanTrash($canTrash)
  {
    $this->canTrash = $canTrash;
  }
  public function getCanTrash()
  {
    return $this->canTrash;
  }
  public function setCanUntrash($canUntrash)
  {
    $this->canUntrash = $canUntrash;
  }
  public function getCanUntrash()
  {
    return $this->canUntrash;
  }
}
