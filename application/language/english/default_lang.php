<?php

/* NOTE: DO NOT CHANGE THIS FILE. IF YOU WANT TO UPDATE THE LANGUAGE THEN COPY THIS FILE TO custom_lang.php AND UPDATE THERE */

/* language locale */
$lang["language_locale"] = "en"; //locale code
$lang["language_locale_long"] = "en-US"; //long locale code

/* common */
$lang["add"] = "Add";
$lang["edit"] = "Edit";
$lang["close"] = "Close";
$lang["cancel"] = "Cancel";
$lang["save"] = "Save";
$lang["delete"] = "Delete";
$lang["description"] = "Description";
$lang["admin"] = "Admin";
$lang["manager"] = "Manager";
$lang["options"] = "Options";
$lang["id"] = "ID";
$lang["name"] = "Name";
$lang["email"] = "Email";
$lang["username"] = "Username";
$lang["password"] = "Password";
$lang["retype_password"] = "Retype password";
$lang["previous"] = "Previous";
$lang["next"] = "Next";
$lang["active"] = "Active";
$lang["inactive"] = "Inactive";
$lang["status"] = "Status";
$lang["start_date"] = "Start date";
$lang["end_date"] = "End date";
$lang["start_time"] = "Start time";
$lang["end_time"] = "End time";
$lang["deadline"] = "Deadline";
$lang["added"] = "Added";
$lang["created_date"] = "Created date";
$lang["created"] = "Created";
$lang["created_by"] = "Created by";
$lang["updated"] = "Updated";
$lang["deleted"] = "Deleted";
$lang["currency"] = "Currency";
$lang["new"] = "New";
$lang["open"] = "Open";
$lang["closed"] = "Closed";
$lang["date"] = "Date";
$lang["yes"] = "Yes";
$lang["no"] = "No";
$lang["add_more"] = "Add more";
$lang["crop"] = "Crop";
$lang["income"] = "Income";
$lang["income_vs_expenses"] = "Income vs Expenses";

$lang["title"] = "Title";
$lang["reset"] = "Reset";
$lang["share_with"] = "Share with";
$lang["company_name"] = "Company name";
$lang["address"] = "Address";
$lang["city"] = "City";
$lang["state"] = "State";
$lang["zip"] = "Zip";
$lang["country"] = "Country";
$lang["phone"] = "Phone";
$lang["private"] = "Private";
$lang["website"] = "Website";

$lang["sunday"] = "Sunday";
$lang["monday"] = "Monday";
$lang["tuesday"] = "Tuesday";
$lang["wednesday"] = "Wednesday";
$lang["thursday"] = "Thursday";
$lang["friday"] = "Friday";
$lang["saturday"] = "Saturday";

$lang["daily"] = "Daily";
$lang["monthly"] = "Monthly";
$lang["weekly"] = "Weekly";
$lang["yearly"] = "Yearly";

$lang["see_all"] = "See All";

/* messages */
$lang["error_occurred"] = "Sorry, an error occurred during processing the action! <br /> Please try again later.";
$lang["field_required"] = "This field is required.";
$lang["end_date_must_be_equal_or_greater_than_start_date"] = "End date must be equal or greater than Start date.";
$lang["date_must_be_equal_or_greater_than_today"] = "Date must be equal or greater than today.";
$lang["enter_valid_email"] = "Please enter a valid email address.";
$lang["enter_same_value"] = "Please enter the same value again.";
$lang["record_saved"] = "The record has been saved.";
$lang["record_updated"] = "The record has been updated.";
$lang["record_cannot_be_deleted"] = "The record is in use, you can't delete the record!";
$lang["record_deleted"] = "The record has been deleted.";
$lang["record_undone"] = "The record has been undone.";
$lang["settings_updated"] = "The settings has been updated.";
$lang["enter_minimum_6_characters"] = "Please enter at least 6 characters.";
$lang["message_sent"] = "The message has been sent.";
$lang["invalid_file_type"] = "File type is not allowed.";
$lang["something_went_wrong"] = "Oops, something went wrong!";
$lang["duplicate_email"] = "The email address you have entered is already registered.";
$lang["comment_submited"] = "The comment has been submitted.";
$lang["no_new_messages"] = "You have no new messages";
$lang["sent_you_a_message"] = "Sent you a message";
$lang["max_file_size_3mb_message"] = "File size should not be larger than 3MB";
$lang["keep_it_blank_to_use_default"] = "Keep it blank to use the default";
$lang["admin_user_has_all_power"] = "Admin user's has power to access/modify everything in this system!";
$lang["no_posts_to_show"] = "No posts to show";

/* team_member */
$lang["add_team_member"] = "Add member";
$lang["edit_team_member"] = "Edit team member";
$lang["delete_team_member"] = "Delete team member";
$lang["team_member"] = "Team member";
$lang["team_members"] = "Team members";
$lang["active_members"] = "Active members";
$lang["inactive_members"] = "Inactive members";
$lang["first_name"] = "First name";
$lang["last_name"] = "Last name";
$lang["mailing_address"] = "Mailing address";
$lang["alternative_address"] = "Alternative address";
$lang["phone"] = "Phone";
$lang["alternative_phone"] = "Alternative phone";
$lang["gender"] = "Gender";
$lang["male"] = "Male";
$lang["female"] = "Female";
$lang["date_of_birth"] = "Date of birth";
$lang["date_of_hire"] = "Date of hire";
$lang["ssn"] = "SSN";
$lang["salary"] = "Salary";
$lang["salary_term"] = "Salary term";
$lang["job_info"] = "Job Info";
$lang["job_title"] = "Job Title";
$lang["general_info"] = "General Info";
$lang["account_settings"] = "Account settings";
$lang["list_view"] = "List view";
$lang["profile_image_changed"] = "The profile image has been changed.";
$lang["send_invitation"] = "Send invitation";
$lang["invitation_sent"] = "The invitation has been sent.";
$lang["reset_info_send"] = "Email sent!<br />Please check your email for instructions.";
$lang["profile"] = "Profile";
$lang["my_profile"] = "My Profile";
$lang["change_password"] = "Change Password";
$lang["social_links"] = "Social Links";
$lang["view_details"] = "View Details";
$lang["invite_someone_to_join_as_a_team_member"] = "Invite someone to join as a team member.";

/* team */
$lang["add_team"] = "Add team";
$lang["edit_team"] = "Edit team";
$lang["delete_team"] = "Delete team";
$lang["team"] = "Team";
$lang["select_a_team"] = "Select a team";

/* dashboard */
$lang["dashboard"] = "Dashboard";

/* attendance */
$lang["add_attendance"] = "Add time manually";
$lang["edit_attendance"] = "Edit time card";
$lang["delete_attendance"] = "Delete time card";
$lang["attendance"] = "Time cards";
$lang["clock_in"] = "Clock In";
$lang["clock_out"] = "Clock Out";
$lang["in_date"] = "In Date";
$lang["out_date"] = "Out Date";
$lang["in_time"] = "In Time";
$lang["out_time"] = "Out Time";
$lang["clock_started_at"] = "Clock started at";
$lang["you_are_currently_clocked_out"] = "You are currently clocked out";
$lang["members_clocked_in"] = "Members Clocked In";
$lang["members_clocked_out"] = "Members Clocked Out";
$lang["my_time_cards"] = "My time cards";
$lang["timecard_statistics"] = "Time Card Statistics";
$lang["total_hours_worked"] = "Total hours worked";
$lang["total_project_hours"] = "Total project hours";

/* leave types */
$lang["add_leave_type"] = "Add leave type";
$lang["edit_leave_type"] = "Edit leave type";
$lang["delete_leave_type"] = "Delete leave type";
$lang["leave_type"] = "Leave type";
$lang["leave_types"] = "Leave types";

/* leave */
$lang["apply_leave"] = "Apply leave";
$lang["assign_leave"] = "Assign leave";
$lang["leaves"] = "Leave";
$lang["pending_approval"] = "Pending approval";
$lang["all_applications"] = "All applications";
$lang["duration"] = "Duration";
$lang["single_day"] = "Single day";
$lang["mulitple_days"] = "Multiple days";
$lang["reason"] = "Reason";
$lang["applicant"] = "Applicant";
$lang["approved"] = "Approved";
$lang["approve"] = "Approve";
$lang["rejected"] = "Rejected";
$lang["reject"] = "Reject";
$lang["canceled"] = "Canceled";
$lang["completed"] = "Completed";
$lang["pending"] = "Pending";
$lang["day"] = "Day";
$lang["days"] = "Days";
$lang["hour"] = "Hour";
$lang["hours"] = "Hours";
$lang["application_details"] = "Application details";
$lang["rejected_by"] = "Rejected by";
$lang["approved_by"] = "Approved by";
$lang["start_date_to_end_date_format"] = "%s to %s";
$lang["my_leave"] = "My leave";

/* events */
$lang["add_event"] = "Add event";
$lang["edit_event"] = "Edit event";
$lang["delete_event"] = "Delete event";
$lang["events"] = "Events";
$lang["event_calendar"] = "Event calendar";
$lang["location"] = "Location";
$lang["event_details"] = "Event details";
$lang["event_deleted"] = "The event has been deleted.";
$lang["view_on_calendar"] = "View on calendar";
$lang["no_event_found"] = "No event found!";
$lang["events_today"] = "Events today";

/* announcement */
$lang["add_announcement"] = "Add announcement";
$lang["edit_announcement"] = "Edit announcement";
$lang["delete_announcement"] = "Delete announcement";
$lang["announcement"] = "Announcement";
$lang["announcements"] = "Announcements";
$lang["all_team_members"] = "All team members";
$lang["all_team_clients"] = "All Clients";

/* settings */
$lang["app_settings"] = "App Settings";
$lang["app_title"] = "App Title";
$lang["site_logo"] = "Site Logo";
$lang["invoice_logo"] = "Invoice Logo";
$lang["timezone"] = "Timezone";
$lang["date_format"] = "Date Format";
$lang["time_format"] = "Time Format";
$lang["first_day_of_week"] = "First Day of Week";
$lang["currency_symbol"] = "Currency Symbol";
$lang["general"] = "General";
$lang["general_settings"] = "General Settings";
$lang["item_purchase_code"] = "Item Purchase Code";
$lang["company"] = "Company";
$lang["company_settings"] = "Company Settings";
$lang["email_settings"] = "Email Settings";
$lang["payment_methods"] = "Payment Methods";
$lang["email_sent_from_address"] = "Email sent from address";
$lang["email_sent_from_name"] = "Email sent from name";
$lang["email_use_smtp"] = "Use SMTP";
$lang["email_smtp_host"] = "SMTP Host";
$lang["email_smtp_user"] = "SMTP User";
$lang["email_smtp_password"] = "SMTP Password";
$lang["email_smtp_port"] = "SMTP Port";
$lang["send_test_mail_to"] = "Send a test mail to";
$lang["test_mail_sent"] = "The test mail has been sent!";
$lang["test_mail_send_failed"] = "Failed to send the test email.";
$lang["settings"] = "Settings";
$lang["updates"] = "Updates";
$lang["current_version"] = "Current Version";
$lang["language"] = "Language";
$lang["ip_restriction"] = "IP Restriction";
$lang["varification_failed_message"] = "Sorry, we could not verify your item purchase code.";
$lang["enter_one_ip_per_line"] = "Enter one IP per line. Keep it blank to allow all IPs. *Admin users will not be affected.";
$lang["allow_timecard_access_from_these_ips_only"] = "Allow timecard access from these IPs only.";
$lang["decimal_separator"] = "Decimal Separator";
$lang["client_settings"] = "Client settings";
$lang["disable_client_login_and_signup"] = "Disable client login and signup";
$lang["disable_client_login_help_message"] = "Client contacts will not be able to login/sign up in this system until you revert this setting.";
$lang["who_can_send_or_receive_message_to_or_from_clients"] = "Who can send/receive message to/from clients";

/* account */
$lang["authentication_failed"] = "Authentication failed!";
$lang["signin"] = "Sign in";
$lang["sign_out"] = "Sign Out";
$lang["you_dont_have_an_account"] = "You don't have an account?";
$lang["already_have_an_account"] = "Already have an account?";
$lang["forgot_password"] = "Forgot password?";
$lang["signup"] = "Sign up";
$lang["input_email_to_reset_password"] = "Input your email to reset your password";
$lang["no_acount_found_with_this_email"] = "Sorry, no account found with this email.";
$lang["reset_password"] = "Reset Password";
$lang["password_reset_successfully"] = "Your password has been reset successfully.";
$lang["account_created"] = "Your account has been created successfully!";
$lang["invitation_expaired_message"] = "The invitation has expired or something went wrong";
$lang["account_already_exists_for_your_mail"] = "Account already exists for your email address.";
$lang["create_an_account_as_a_new_client"] = "Create an account as a new client.";
$lang["create_an_account_as_a_team_member"] = "Create an account as a team member.";
$lang["create_an_account_as_a_client_contact"] = "Create an account as a client contact.";

/* messages */
$lang["messages"] = "Messages";
$lang["message"] = "Message";
$lang["compose"] = "Compose";
$lang["send_message"] = "Send message";
$lang["write_a_message"] = "Write a message...";
$lang["reply_to_sender"] = "Reply to sender...";
$lang["subject"] = "Subject";
$lang["send"] = "Send";
$lang["to"] = "To";
$lang["from"] = "From";
$lang["inbox"] = "Inbox";
$lang["sent_items"] = "Sent items";
$lang["me"] = "Me";
$lang["select_a_message"] = "Select a message to view";

/* clients */
$lang["add_client"] = "Add client";
$lang["edit_client"] = "Edit client";
$lang["delete_client"] = "Delete client";
$lang["client"] = "Client";
$lang["clients"] = "Clients";
$lang["client_details"] = "Client details";
$lang["due"] = "Due";

$lang["add_contact"] = "Add contact";
$lang["edit_contact"] = "Edit contact";
$lang["delete_contact"] = "Delete contact";
$lang["contact"] = "Contact";
$lang["contacts"] = "Contacts";
$lang["users"] = "Users";
$lang["primary_contact"] = "Primary contact";
$lang["disable_login"] = "Disable login";
$lang["disable_login_help_message"] = "The user will not be able to login in this system!";
$lang["email_login_details"] = "Email login details to this user";
$lang["generate"] = "Generate";
$lang["show_text"] = "Show text";
$lang["hide_text"] = "Hide text";
$lang["mark_as_inactive"] = "Mark as inactive";
$lang["mark_as_inactive_help_message"] = "The inactive users will not be able to login in this system and not be counted in the active user list!";

$lang["invoice_id"] = "Invoice ID";
$lang["payments"] = "Payments";
$lang["invoice_sent_message"] = "The invoice has been sent!";
$lang["attached"] = "Attached";
$lang["vat_number"] = "VAT Number";
$lang["invite_an_user"] = "Invite an user for %s"; // Invite an user for {company name}
$lang["unit_type"] = "Unit type";

/* projects */
$lang["add_project"] = "Add project";
$lang["edit_project"] = "Edit project";
$lang["delete_project"] = "Delete project";
$lang["project"] = "Project";
$lang["projects"] = "Projects";
$lang["all_projects"] = "All Projects";
$lang["member"] = "Member";
$lang["overview"] = "Overview";
$lang["project_members"] = "Project members";
$lang["add_member"] = "Add member";
$lang["delete_member"] = "Delete member";
$lang["start_timer"] = "Start timer";
$lang["stop_timer"] = "Stop timer";
$lang["project_timeline"] = "Project Timeline";
$lang["open_projects"] = "Open Projects";
$lang["projects_completed"] = "Projects Completed";
$lang["progress"] = "Progress";
$lang["activity"] = "Activity";
$lang["started_at"] = "Started at";
$lang["customer_feedback"] = "Customer feedback";
$lang["project_comment_reply"] = "Project comment reply";
$lang["task_comment_reply"] = "Task comment reply";
$lang["file_comment_reply"] = "File comment reply   ";
$lang["customer_feedback_reply"] = "Customer feedback reply";

/* expense */
$lang["add_category"] = "Add category";
$lang["edit_category"] = "Edit category";
$lang["delete_category"] = "Delete category";
$lang["category"] = "Category";
$lang["categories"] = "Categories";
$lang["expense_categories"] = "Expense Categories";
$lang["add_expense"] = "Add expense";
$lang["edit_expense"] = "Edit expense";
$lang["delete_expense"] = "Delete expense";
$lang["expense"] = "Expense";
$lang["expenses"] = "Expenses";
$lang["date_of_expense"] = "Date of expense";
$lang["finance"] = "Finance";

/* notes */
$lang["add_note"] = "Add note";
$lang["edit_note"] = "Edit note";
$lang["delete_note"] = "Delete note";
$lang["note"] = "Note";
$lang["notes"] = "Notes";
$lang["sticky_note"] = "Sticky Note (Private)";

/* history */
$lang["history"] = "History";

/* timesheet */
$lang["timesheets"] = "Timesheets";
$lang["log_time"] = "Log time";
$lang["edit_timelog"] = "Edit timelog";
$lang["delete_timelog"] = "Delete timelog";
$lang["timesheet_statistics"] = "Timesheet Statistics";

/* milestones */
$lang["add_milestone"] = "Add milestone";
$lang["edit_milestone"] = "Edit milestone";
$lang["delete_milestone"] = "Delete milestone";
$lang["milestone"] = "Milestone";
$lang["milestones"] = "Milestones";

/* files */
$lang["add_files"] = "Add files";
$lang["edit_file"] = "Edit file";
$lang["delete_file"] = "Delete file";
$lang["file"] = "File";
$lang["files"] = "Files";
$lang["file_name"] = "File name";
$lang["size"] = "Size";
$lang["uploaded_by"] = "Uploaded by";
$lang["accepted_file_format"] = "Accepted file format";
$lang["comma_separated"] = "Comma separated";
$lang["project_file"] = "File";
$lang["download"] = "Download";
$lang["download_files"] = "Download %s files"; //Ex. Download 4 files
$lang["file_preview_is_not_available"] = "File preview is not available.";

/* tasks */
$lang["add_task"] = "Add task";
$lang["edit_task"] = "Edit task";
$lang["delete_task"] = "Delete task";
$lang["task"] = "Task";
$lang["tasks"] = "Tasks";
$lang["my_tasks"] = "My Tasks";
$lang["my_open_tasks"] = "My open tasks";
$lang["assign_to"] = "Assign to";
$lang["assigned_to"] = "Assigned to";
$lang["labels"] = "Labels";
$lang["to_do"] = "To do";
$lang["in_progress"] = "In progress";
$lang["done"] = "Done";
$lang["task_info"] = "Task info";
$lang["points"] = "Points";
$lang["point"] = "Point";
$lang["task_status"] = "Task Status";

/* comments */
$lang["comment"] = "Comment";
$lang["comments"] = "Comments";
$lang["write_a_comment"] = "Write a comment...";
$lang["write_a_reply"] = "Write a reply...";
$lang["post_comment"] = "Post Comment";
$lang["post_reply"] = "Post Reply";
$lang["reply"] = "Reply";
$lang["replies"] = "Replies";
$lang["like"] = "Like";
$lang["unlike"] = "Unlike";
$lang["view"] = "View";
$lang["project_comment"] = "Project Comment";
$lang["task_comment"] = "Task Comment";
$lang["file_comment"] = "File Comment";

/* time format */
$lang["today"] = "Today";
$lang["yesterday"] = "Yesterday";
$lang["tomorrow"] = "Tomorrow";

$lang["today_at"] = "Today at";
$lang["yesterday_at"] = "Yesterday at";

/* tickets */

$lang["add_ticket"] = "Add ticket";
$lang["ticket"] = "Ticket";
$lang["tickets"] = "Tickets";
$lang["ticket_id"] = "Ticket ID";
$lang["client_replied"] = "Client replied";
$lang["change_status"] = "Change status";
$lang["last_activity"] = "Last activity";
$lang["open_tickets"] = "Open tickets";
$lang["ticket_status"] = "Ticket Status";

/* ticket types */

$lang["add_ticket_type"] = "Add ticket type";
$lang["ticket_type"] = "Ticket type";
$lang["ticket_types"] = "Ticket types";
$lang["edit_ticket_type"] = "Edit ticket type";
$lang["delete_ticket_type"] = "Delete ticket type";

/* payment methods */

$lang["add_payment_method"] = "Add payment method";
$lang["payment_method"] = "Payment method";
$lang["payment_methods"] = "Payment methods";
$lang["edit_payment_method"] = "Edit payment method";
$lang["delete_payment_method"] = "Delete payment method";

/* invoices */

$lang["add_invoice"] = "Add invoice";
$lang["edit_invoice"] = "Edit invoice";
$lang["delete_invoice"] = "Delete invoice";
$lang["invoice"] = "Invoice";
$lang["invoices"] = "Invoices";
$lang["bill_date"] = "Bill date";
$lang["due_date"] = "Due date";
$lang["payment_date"] = "Payment date";
$lang["bill_to"] = "Bill To";
$lang["invoice_value"] = "Invoice Value";
$lang["payment_received"] = "Payment Received";
$lang["invoice_payments"] = "Payments";
$lang["draft"] = "Draft";
$lang["fully_paid"] = "Fully paid";
$lang["partially_paid"] = "Partially paid";
$lang["not_paid"] = "Not paid";
$lang["overdue"] = "Overdue";
$lang["invoice_items"] = "Invoice items";
$lang["edit_invoice"] = "Edit invoice";
$lang["delete_invoice"] = "Delete invoice";
$lang["item"] = "Item";
$lang["add_item"] = "Add item";
$lang["create_new_item"] = "Create new item";
$lang["select_or_create_new_item"] = "Select from list or create new item...";
$lang["quantity"] = "Quantity";
$lang["rate"] = "Rate";
$lang["total_of_all_pages"] = "Total of all pages";
$lang["sub_total"] = "Sub Total";
$lang["total"] = "Total";
$lang["last_email_sent"] = "Last email sent";
$lang["item_library"] = "Item library";
$lang["add_payment"] = "Add payment";
$lang["never"] = "Never";
$lang["email_invoice_to_client"] = "Email invoice to client";
$lang["download_pdf"] = "Download PDF";
$lang["print"] = "Print";
$lang["actions"] = "Actions";
$lang["balance_due"] = "Balance Due";
$lang["paid"] = "Paid";
$lang["amount"] = "Amount";
$lang["invoice_payment_list"] = "Invoice payment list";
$lang["invoice_statistics"] = "Invoice Statistics";
$lang["payment"] = "Payment";

/* email templates */
$lang["email_templates"] = "Email templates";
$lang["select_a_template"] = "Select a template to edit";
$lang["avilable_variables"] = "Available variables";
$lang["restore_to_default"] = "Restore to default";
$lang["template_restored"] = "The template has been restored to default.";
$lang["login_info"] = "Login info";
$lang["reset_password"] = "Reset password";
$lang["team_member_invitation"] = "Team member invitation";
$lang["client_contact_invitation"] = "Client contact invitation";
$lang["send_invoice"] = "Send invoice";
$lang["signature"] = "Signature";

/* roles */

$lang["role"] = "Role";
$lang["roles"] = "Roles";
$lang["add_role"] = "Add role";
$lang["edit_role"] = "Edit role";
$lang["delete_role"] = "Delete role";
$lang["use_seetings_from"] = "Use settings from";
$lang["permissions"] = "Permissions";
$lang["yes_all_members"] = "Yes, all members";
$lang["yes_specific_members_or_teams"] = "Yes, specific members or teams";
$lang["yes_specific_ticket_types"] = "Yes, specific ticket types";
$lang["select_a_role"] = "Select a role";
$lang["choose_members_and_or_teams"] = "Choose members and / or teams";
$lang["choose_ticket_types"] = "Choose ticket types";
$lang["excluding_his_her_time_cards"] = "Excluding his/her own time cards";
$lang["excluding_his_her_leaves"] = "Excluding his/her own leaves";
$lang["can_manage_team_members_leave"] = "Can manage team member's leaves?";
$lang["can_manage_team_members_timecards"] = "Can manage team member's time cards?";
$lang["can_access_invoices"] = "Can access invoices?";
$lang["can_access_expenses"] = "Can access expenses?";
$lang["can_access_clients_information"] = "Can access client's information?";
$lang["can_access_tickets"] = "Can access tickets?";
$lang["can_manage_announcements"] = "Can manage announcements?";

/* timeline */
$lang["post_placeholder_text"] = "Share an idea or documents...";
$lang["post"] = "Post";
$lang["timeline"] = "Timeline";
$lang["load_more"] = "Load more";
$lang["upload_file"] = "Upload File";
$lang["upload"] = "Upload";
$lang["new_posts"] = "New posts";

/* taxes */

$lang["add_tax"] = "Add Tax";
$lang["tax"] = "TAX";
$lang["taxes"] = "Taxes";
$lang["edit_tax"] = "Edit tax";
$lang["delete_tax"] = "Delete tax";
$lang["percentage"] = "Percentage (%)";
$lang["second_tax"] = "Second TAX";

/* Version 1.2 */
$lang["available_on_invoice"] = "Available on Invoice";
$lang["available_on_invoice_help_text"] = "The payment method will be appear in client's invoices.";
$lang["minimum_payment_amount"] = "Minimum payment amount";
$lang["minimum_payment_amount_help_text"] = "Clients will not be able to pay the invoice using this payment method, if the invoice value less than this value.";
$lang["pay_invoice"] = "Pay Invoice";
$lang["pay_button_text"] = "Pay button text";
$lang["minimum_payment_validation_message"] = "The payment amount can't be less then: "; //ex. The payment amount can't be less then: USD 100.00
$lang["invoice_settings"] = "Invoice Settings";
$lang["allow_partial_invoice_payment_from_clients"] = "Allow partial payment from clients";
$lang["invoice_color"] = "Invoice Color";
$lang["invoice_footer"] = "Invoice Footer";
$lang["invoice_preview"] = "Invoice Preview";
$lang["close_preview"] = "Close Preview";
$lang["only_me"] = "Only me";
$lang["specific_members_and_teams"] = "Specific members and teams";
$lang["rows_per_page"] = "Rows per page";
$lang["price"] = "Price";
$lang["security_type"] = "Security Type";

$lang["client_can_view_tasks"] = "Client can view tasks?";
$lang["client_can_create_tasks"] = "Client can create tasks?";
$lang["client_can_edit_tasks"] = "Client can edit tasks?";
$lang["client_can_comment_on_tasks"] = "Client can comment on tasks?";

$lang["set_project_permissions"] = "Set project permissions";
$lang["can_create_projects"] = "Can create projects";
$lang["can_edit_projects"] = "Can edit projects";
$lang["can_delete_projects"] = "Can delete projects";
$lang["can_create_tasks"] = "Can create tasks";
$lang["can_edit_tasks"] = "Can edit tasks";
$lang["can_delete_tasks"] = "Can delete tasks";
$lang["can_comment_on_tasks"] = "Can comment on tasks";
$lang["can_create_milestones"] = "Can create milestones";
$lang["can_edit_milestones"] = "Can edit milestones";
$lang["can_delete_milestones"] = "Can delete milestones";
$lang["can_add_remove_project_members"] = "Can add/remove project members";
$lang["can_delete_files"] = "Can delete files";

/* Version 1.2.2 */
$lang["label"] = "Label";
$lang["send_bcc_to"] = "When sending invoice to client, send BCC to";
$lang["mark_project_as_completed"] = "Mark Project as Completed";
$lang["mark_project_as_canceled"] = "Mark Project as Canceled";
$lang["mark_project_as_open"] = "Mark Project as Open";

/* Version 1.3 */
$lang["notification"] = "Notification";
$lang["notifications"] = "Notifications";
$lang["notification_settings"] = "Notification Settings";
$lang["enable_email"] = "Enable email";
$lang["enable_web"] = "Enable web";
$lang["event"] = "Event";
$lang["notify_to"] = "Notify to";

$lang["project_created"] = "Project created";
$lang["project_deleted"] = "Project deleted";
$lang["project_task_created"] = "Project task created";
$lang["project_task_updated"] = "Project task updated";
$lang["project_task_assigned"] = "Project task assigned";
$lang["project_task_started"] = "Project task started";
$lang["project_task_finished"] = "Project task finished";
$lang["project_task_reopened"] = "Project task reopened";
$lang["project_task_deleted"] = "Project task deleted";
$lang["project_task_commented"] = "Project task commented";
$lang["project_member_added"] = "Project member added";
$lang["project_member_deleted"] = "Project member deleted";
$lang["project_file_added"] = "Project file added";
$lang["project_file_deleted"] = "Project file deleted";
$lang["project_file_commented"] = "Project file commented";
$lang["project_comment_added"] = "Project comment added";
$lang["project_comment_replied"] = "Project comment replied";
$lang["project_customer_feedback_added"] = "Project customer feedback added";
$lang["project_customer_feedback_replied"] = "Project customer feedback replied";
$lang["client_signup"] = "Client signup";
$lang["invoice_online_payment_received"] = "Invoice online payment received";
$lang["leave_application_submitted"] = "Leave application submitted";
$lang["leave_approved"] = "Leave approved";
$lang["leave_assigned"] = "Leave assigned";
$lang["leave_rejected"] = "Leave rejected";
$lang["leave_canceled"] = "Leave canceled";
$lang["ticket_created"] = "Ticket created";
$lang["ticket_commented"] = "Ticket commented";
$lang["ticket_closed"] = "Ticket closed";
$lang["ticket_reopened"] = "Ticket reopened";
$lang["leave"] = "Leave";

$lang["client_primary_contact"] = "Primary contact of client";
$lang["client_all_contacts"] = "All contacts of client";
$lang["task_assignee"] = "Task assignee";
$lang["task_collaborators"] = "Task collaborators";
$lang["comment_creator"] = "Comment creator";
$lang["leave_applicant"] = "Leave applicant";
$lang["ticket_creator"] = "Ticket creator";

$lang["no_new_notifications"] = "No notification found.";

/* Notification messages */

$lang["notification_project_created"] = "Created a new project.";
$lang["notification_project_deleted"] = "Deleted a project.";
$lang["notification_project_task_created"] = "Created a new task.";
$lang["notification_project_task_updated"] = "Updated a task.";
$lang["notification_project_task_assigned"] = "Assigned a task to %s"; //Assigned a task to Mr. X
$lang["notification_project_task_started"] = "Started a task.";
$lang["notification_project_task_finished"] = "Finished a task.";
$lang["notification_project_task_reopened"] = "Reopened a task.";
$lang["notification_project_task_deleted"] = "Deleted a task.";
$lang["notification_project_task_commented"] = "Commented on a task.";
$lang["notification_project_member_added"] = "Added %s in a project."; //Added Mr. X in a project.
$lang["notification_project_member_deleted"] = "Deleted %s from a project."; //Deleted Mr. X from a project.
$lang["notification_project_file_added"] = "Added a file in project.";
$lang["notification_project_file_deleted"] = "Deleted a file from project.";
$lang["notification_project_file_commented"] = "Commented on a file.";
$lang["notification_project_comment_added"] = "Commented on a project.";
$lang["notification_project_comment_replied"] = "Replied on a project comment.";
$lang["notification_project_customer_feedback_added"] = "Commented on a project.";
$lang["notification_project_customer_feedback_replied"] = "Replied on a comment.";
$lang["notification_client_signup"] = "Signed up as a new client."; //Mr. X signed up as a new client.
$lang["notification_invoice_online_payment_received"] = "Submitted a online payment.";
$lang["notification_leave_application_submitted"] = "Submitted a leave application.";
$lang["notification_leave_approved"] = "Approved a leave of %s."; //Approved a leave of Mr. X
$lang["notification_leave_assigned"] = "Assigned a leave to %s."; //Assigned a leave to Mr. X
$lang["notification_leave_rejected"] = "Rejected a leave %s."; //Approve a leave of Mr. X
$lang["notification_leave_canceled"] = "Canceled a leave appliction.";
$lang["notification_ticket_created"] = "Created a new ticket.";
$lang["notification_ticket_commented"] = "Commented on a ticket.";
$lang["notification_ticket_closed"] = "Closed the ticket.";
$lang["notification_ticket_reopened"] = "Reopened the ticket.";

$lang["general_notification"] = "General notification";

$lang["disable_online_payment"] = "Disable online payment";
$lang["disable_online_payment_description"] = "Hide online payment options in invoice for this client.";

$lang["client_can_view_project_files"] = "Client can view project files?";
$lang["client_can_add_project_files"] = "Client can add project files?";
$lang["client_can_comment_on_files"] = "Client can comment on files?";
$lang["mark_invoice_as_not_paid"] = "Mark as Not paid"; //Change invoice status to Not Paid

$lang["set_team_members_permission"] = "Set team members permissions";
$lang["can_view_team_members_contact_info"] = "Can view team member's contact info?";
$lang["can_view_team_members_social_links"] = "Can view team member's social links?";

$lang["collaborator"] = "Collaborator";
$lang["collaborators"] = "Collaborators";

/* Version 1.4 */

$lang["modules"] = "Modules";
$lang["manage_modules"] = "Manage Modules";
$lang["module_settings_instructions"] = "Select the modules you want to use.";

$lang["task_point_help_text"] = "Task point considered as a task value. You can set 5 points for very difficult tasks and 1 point for easy tasks."; //meaning of task point

$lang["mark_as_open"] = "Mark as Open";
$lang["mark_as_closed"] = "Mark as Closed";

$lang["ticket_assignee"] = "Ticket assignee";

$lang["estimate"] = "Estimate";
$lang["estimates"] = "Estimates";
$lang["estimate_request"] = "Estimate Request";
$lang["estimate_requests"] = "Estimate Requests";
$lang["estimate_list"] = "Estimate List";
$lang["estimate_forms"] = "Estimate Forms";
$lang["estimate_request_forms"] = "Estimate Request Forms";

$lang["add_form"] = "Add form";
$lang["edit_form"] = "Edit form";
$lang["delete_form"] = "Delete form";

$lang["add_field"] = "Add field";
$lang["placeholder"] = "Placeholder";
$lang["required"] = "Required";

$lang["field_type"] = "Field Type";
$lang["preview"] = "Preview";

$lang["field_type_text"] = "Text";
$lang["field_type_textarea"] = "Textarea";
$lang["field_type_select"] = "Select";
$lang["field_type_multi_select"] = "Multi Select";

$lang["request_an_estimate"] = "Request an Estimate";
$lang["estimate_submission_message"] = "Your request has been submitted successfully!";

$lang["hold"] = "Hold";
$lang["processing"] = "Processing";
$lang["estimated"] = "Estimated";

$lang["add_estimate"] = "Add estimate";
$lang["edit_estimate"] = "Edit estimate";
$lang["delete_estimate"] = "Delete estimate";
$lang["valid_until"] = "Valid until";
$lang["estimate_date"] = "Estimate date";
$lang["accepted"] = "Accepted";
$lang["declined"] = "Declined";
$lang["sent"] = "Sent";
$lang["estimate_preview"] = "Estimate Preview";
$lang["estimate_to"] = "Estimate To";

$lang["can_access_estimates"] = "Can access estimates?";
$lang["request_an_estimate"] = "Request an estimate";
$lang["estimate_request_form_selection_title"] = "Please select a form from the following list to submit your request.";

$lang["mark_as_processing"] = "Mark as Processing";
$lang["mark_as_estimated"] = "Mark as Estimated";
$lang["mark_as_hold"] = "Mark as Hold";
$lang["mark_as_canceled"] = "Mark as Canceled";

$lang["mark_as_sent"] = "Mark as Sent";
$lang["mark_as_accepted"] = "Mark as Accepted";
$lang["mark_as_rejected"] = "Mark as Rejected";
$lang["mark_as_declined"] = "Mark as Declined";

$lang["estimate_request_received"] = "Estimate request received";
$lang["estimate_sent"] = "Estimate sent";
$lang["estimate_accepted"] = "Estimate accepted";
$lang["estimate_rejected"] = "Estimate rejected";

$lang["notification_estimate_request_received"] = "Submitted an estimate request";
$lang["notification_estimate_sent"] = "Sent an estimate";
$lang["notification_estimate_accepted"] = "Accepted an estimate";
$lang["notification_estimate_rejected"] = "Rejected an estimate";

$lang["clone_project"] = "Clone Project";
$lang["copy_tasks"] = "Copy tasks";
$lang["copy_project_members"] = "Copy project members";
$lang["copy_milestones"] = "Copy milestones";
$lang["copy_same_assignee_and_collaborators"] = "Copy same assignee and collaborators";
$lang["copy_tasks_start_date_and_deadline"] = "Copy tasks start date and deadline";
$lang["task_comments_will_not_be_included"] = "Tasks comments will not be included";
$lang["project_cloned_successfully"] = "The project has been cloned successfully";

$lang["search"] = "Search";
$lang["no_record_found"] = "No record found.";
$lang["excel"] = "Excel";
$lang["print_button_help_text"] = "Press escape when finished.";
$lang["are_you_sure"] = "Are you sure?";
$lang["file_upload_instruction"] = "Drag-and-drop documents here <br /> (or click to browse...)";
$lang["file_name_too_long"] = "Filename is too long.";
$lang["scrollbar"] = "Scrollbar";

$lang["short_sunday"] = "Sun";
$lang["short_monday"] = "Mon";
$lang["short_tuesday"] = "Tue";
$lang["short_wednesday"] = "Wed";
$lang["short_thursday"] = "Thu";
$lang["short_friday"] = "Fri";
$lang["short_saturday"] = "Sat";

$lang["min_sunday"] = "Su";
$lang["min_monday"] = "Mo";
$lang["min_tuesday"] = "Tu";
$lang["min_wednesday"] = "We";
$lang["min_thursday"] = "Th";
$lang["min_friday"] = "Fr";
$lang["min_saturday"] = "Sa";

$lang["january"] = "January";
$lang["february"] = "February";
$lang["march"] = "March";
$lang["april"] = "April";
$lang["may"] = "May";
$lang["june"] = "June";
$lang["july"] = "July";
$lang["august"] = "August";
$lang["september"] = "September";
$lang["october"] = "October";
$lang["november"] = "November";
$lang["december"] = "December";

$lang["short_january"] = "Jan";
$lang["short_february"] = "Feb";
$lang["short_march"] = "Mar";
$lang["short_april"] = "Apr";
$lang["short_may"] = "May";
$lang["short_june"] = "Jun";
$lang["short_july"] = "Jul";
$lang["short_august"] = "Aug";
$lang["short_september"] = "Sep";
$lang["short_october"] = "Oct";
$lang["short_november"] = "Nov";
$lang["short_december"] = "Dec";

/* Version 1.5 */

$lang["no_such_file_or_directory_found"] = "No such file or directory.";
$lang["gantt"] = "Gantt";
$lang["not_specified"] = "Not specified";
$lang["group_by"] = "Group by";
$lang["create_invoice"] = "Create Invoice";
$lang["include_all_items_of_this_estimate"] = "Include all items of this estimate";
$lang["edit_payment"] = "Edit payment";
$lang["disable_client_login"] = "Disable client login";
$lang["disable_client_signup"] = "Disable client signup";

$lang["chart"] = "Chart";
$lang["signin_page_background"] = "Signin page background";
$lang["show_logo_in_signin_page"] = "Show logo in signin page";
$lang["show_background_image_in_signin_page"] = "Show background image in signin page";

/* Version 1.6 */

$lang["more"] = "More";
$lang["custom"] = "Custom";
$lang["clear"] = "Clear";
$lang["expired"] = "Expired";
$lang["enable_attachment"] = "Enable attachment";
$lang["custom_fields"] = "Custom fields";
$lang["edit_field"] = "Edit field";
$lang["delete_field"] = "Delete field";
$lang["client_info"] = "Client info";
$lang["edit_expenses_category"] = "Edit expenses category";
$lang["eelete_expenses_category"] = "Delete expenses category";
$lang["empty_starred_projects"] = "To access your favorite projects quickly, please go to the project view and mark the star.";
$lang["empty_starred_clients"] = "To access your favorite clients quickly, please go to the client view and mark the star.";
$lang["download_zip_name"] = "documents";
$lang["invoice_prefix"] = "Invoice prefix";
$lang["invoice_style"] = "Invoice style";
$lang["delete_confirmation_message"] = " Are you sure? You won't be able to undo this action!";
$lang["left"] = "Left";
$lang["right"] = "Right";
$lang["currency_position"] = "Currency Position";
$lang["recipient"] = "Recipient";

$lang["new_message_sent"] = "New message sent";
$lang["message_reply_sent"] = "Message replied";
$lang["notification_new_message_sent"] = "Sent a message.";
$lang["notification_message_reply_sent"] = "Replied a message.";
$lang["invoice_payment_confirmation"] = "Invoice payment confirmation";
$lang["notification_invoice_payment_confirmation"] = "Payment received";

/* Version 1.7 */

$lang["client_can_create_projects"] = "Client can create projects?";
$lang["client_can_view_timesheet"] = "Client can view timesheet?";
$lang["client_can_view_gantt"] = "Client can view gantt?";
$lang["client_can_view_overview"] = "Client can view project overview?";
$lang["client_can_view_milestones"] = "Client can view milestones?";

$lang["items"] = "Items";
$lang["edit_item"] = "Edit item";
$lang["item_edit_instruction"] = "Note: The changes will not be affected on existing invoices or estimates.";

$lang["recurring"] = "Recurring";
$lang["repeat_every"] = "Repeat every"; //Ex. repeat every 2 months
$lang["interval_days"] = "Day(s)";
$lang["interval_weeks"] = "Week(s)";
$lang["interval_months"] = "Month(s)";
$lang["interval_years"] = "Year(s)";
$lang["cycles"] = "Cycles";
$lang["recurring_cycle_instructions"] = "Recurring will be stopped after the number of cycles. Keep it blank for infinity.";
$lang["next_recurring_date"] = "Next recurring";
$lang["stopped"] = "Stopped";
$lang["past_recurring_date_error_message_title"] = "The selected bill date and repeat type returns a past date.";
$lang["past_recurring_date_error_message"] = "Next recurring date must be a future date. Please enter a future date.";
$lang["sub_invoices"] = "Sub invoices";

$lang["cron_job_required"] = "Cron Job is required for this action!";

$lang["recurring_invoice_created_vai_cron_job"] = "Recurring invoice created via Cron Job";
$lang["notification_recurring_invoice_created_vai_cron_job"] = "New invoice generated";

$lang["field_type_number"] = "Number";
$lang["show_in_table"] = "Show in table";
$lang["show_in_invoice"] = "Show in invoice";
$lang["visible_to_admins_only"] = "Visible to admins only";
$lang["hide_from_clients"] = "Hide from clients";
$lang["public"] = "Public";

$lang["help"] = "Help";
$lang["articles"] = "Articles";
$lang["add_article"] = "Add new article";
$lang["edit_article"] = "Edit article";
$lang["delete_article"] = "Delete article";
$lang["can_manage_help_and_knowledge_base"] = "Can manage help and knowledge base?";

$lang["how_can_we_help"] = "How can we help?";
$lang["help_page_title"] = "Internal Wiki";
$lang["search_your_question"] = "Search your question";
$lang["no_result_found"] = "No result found.";
$lang["sort"] = "Sort";
$lang["total_views"] = "Total views";

$lang["help_and_support"] = "Help & Support";
$lang["knowledge_base"] = "Knowledge base";

$lang["payment_success_message"] = "Your payment has been completed.";
$lang["payment_card_charged_but_system_error_message"] = "You card may be charged but we can't complete the process. Please contact to your system admin.";
$lang["card_payment_failed_error_message"] = "We can't process your payment right now, so please try again later.";

$lang["message_received"] = "Message received";
$lang["in_number_of_days"] = "In %s days"; //Ex. In 7 days
$lang["details"] = "Details";
$lang["summary"] = "Summary";
$lang["project_timesheet"] = "Project timesheet";

$lang["set_event_permissions"] = "Set event permissions";
$lang["disable_event_sharing"] = "Disable event sharing";
$lang["can_update_team_members_general_info_and_social_links"] = "Can update team member's general info and social links?";
$lang["can_manage_team_members_project_timesheet"] = "Can manage team member's project timesheet?";

$lang["cron_job"] = "Cron Job";
$lang["cron_job_link"] = "Cron Job link";
$lang["last_cron_job_run"] = "Last Cron Job run";
$lang["created_from"] = "Created from"; //Ex. Created from Invoice#1
$lang["recommended_execution_interval"] = "Recommended execution interval";

/* Version 1.8 */

$lang["integration"] = "Integration";
$lang["get_your_key_from_here"] = "Get your key from here:";
$lang["re_captcha_site_key"] = "Site key";
$lang["re_captcha_secret_key"] = "Secret key";

$lang["re_captcha_error-missing-input-secret"] = "reCAPTCHA secret is missing";
$lang["re_captcha_error-invalid-input-secret"] = "reCAPTCHA secret is not valid.";
$lang["re_captcha_error-missing-input-response"] = "Please select the reCAPTCHA.";
$lang["re_captcha_error-invalid-input-response"] = "The response parameter is invalid or malformed.";
$lang["re_captcha_error-bad-request"] = "The request is invalid or malformed.";
$lang["re_captcha_expired"] = "The reCAPTCHA has been expired. Please reload the page.";

$lang["yes_all_tickets"] = "Yes, all tickets";
$lang["choose_ticket_types"] = "Choose ticket types";

$lang["can_manage_all_projects"] = "Can manage all projects";
$lang["show_most_recent_ticket_comments_at_the_top"] = "Show most recent ticket comments at the top";

$lang["new_event_added_in_calendar"] = "New event added in calendar";
$lang["notification_new_event_added_in_calendar"] = "Added a new event.";

$lang["todo"] = "To do";
$lang["add_a_todo"] = "Add a to do...";

/* Version 1.9 */

$lang["client_groups"] = "Client groups";
$lang["add_client_group"] = "Add client group";
$lang["edit_client_group"] = "Edit client group";
$lang["delete_client_group"] = "Delete client group";

$lang["ticket_prefix"] = "Ticket prefix";
$lang["add_a_task"] = "Add a task...";

$lang["add_task_status"] = "Add task status";
$lang["edit_task_status"] = "Edit task status";
$lang["delete_task_status"] = "Delete task status";

$lang["list"] = "List";
$lang["kanban"] = "Kanban";
$lang["priority"] = "Priority";
$lang["moved_up"] = "Moved Up";
$lang["moved_down"] = "Moved Down";
$lang["mark_project_as_hold"] = "Mark Project as Hold";

$lang["repeat"] = "Repeat";

$lang["hide_team_members_list"] = "Hide team members list?";

/* Version 2.0 */

$lang["summary_details"] = "Summary details";

$lang["chat"] = "Chat";
$lang["my_preferences"] = "My preferences";
$lang["show_push_notification"] = "Show push notification";
$lang["notification_sound_volume"] = "Notification sound volume";

$lang["project_reference_in_tickets"] = "Enable project reference";

$lang["hide_menus_from_client_portal"] = "Hide menus from client portal";
$lang["hidden_menus"] = "Hidden menus";

$lang["new_announcement_created"] = "New announcement created";
$lang["notification_new_announcement_created"] = "Created an announcement.";

$lang["month"] = "Month";
$lang["profit"] = "Profit";

$lang["invoice_due_reminder_before_due_date"] = "Invoice due reminder before due date";
$lang["send_due_invoice_reminder_notification_before"] = "Send due invoice reminder before due date";
$lang["send_invoice_overdue_reminder_after"] = "Send invoice overdue reminder after";
$lang["invoice_overdue_reminder"] = "Invoice overdue reminder";
$lang["recurring_invoice_creation_reminder"] = "Recurring invoice creation reminder";
$lang["send_recurring_invoice_reminder_before_creation"] = "Send recurring invoice reminder before creation";

$lang["notification_invoice_due_reminder_before_due_date"] = "Reminder: Invoice due.";
$lang["notification_invoice_overdue_reminder"] = "Reminder: Invoice overdue";
$lang["notification_recurring_invoice_creation_reminder"] = "An invoice will be generated soon.";

$lang["can_delete_leave_application"] = "Can delete leave application?";
$lang["no_of_decimals"] = "No. of decimals";

$lang["checklist"] = "Checklist";
$lang["delete_checklist_item"] = "Delete checklist item";

$lang["save_and_show"] = "Save & show";
$lang["total_leave_yearly"] = "Total Leave (Yearly)";

$lang["new_conversation"] = "New conversation";

$lang["enable_web_notification"] = "Enable web notification";
$lang["enable_email_notification"] = "Enable email notification";

/* Version 2.0.3 */

$lang["show_in_estimate"] = "Show in estimate";
$lang["mentioned_members"] = "Mentioned members";
$lang["all"] = "All";

$lang["confirmed"] = "Confirmed";
$lang["confirm"] = "Confirm";

$lang["confirmed_by"] = "Confirmed by";
$lang["confirm_event"] = "Confirm event";
$lang["reject_event"] = "Reject event";
$lang["event_status"] = "Event status";

$lang["specific_client_contacts"] = "Specific client contacts";
$lang["choose_client_contacts"] = "Choose client contacts";
$lang["invitations_sent"] = "The invitations has been sent.";

/* Version 2.1 */

$lang["add_new_dashboard"] = "Add new dashboard";
$lang["add_row"] = "Add row";

$lang["available_widgets"] = "Available Widgets";
$lang["your_selected_widgets_will_be_appear_here"] = "Your selected widgets will be appear here";
$lang["drag_and_drop_widgets_here"] = "Drag and drop widgets here";
$lang["no_more_widgets_available"] = "No more widgets available";
$lang["invalid_widget_access"] = "You don't have permission to access this widget";

$lang["dashboard_title"] = "Dashboard title";
$lang["edit_dashboard"] = "Edit dashboard";
$lang["edit_title"] = "Edit title";
$lang["default_dashboard"] = "Default dashboard";

$lang["widget"] = "Widget";
$lang["widgets"] = "Widgets";
$lang["add_widget"] = "Add widget";
$lang["edit_widget"] = "Edit widget";
$lang["delete_widget"] = "Delete widget";

$lang["content"] = "Content";
$lang["clock_in_out"] = "Clock in-out";
$lang["custom_widget_details"] = "Custom widget details";

$lang["total_projects"] = "Total projects";
$lang["total_invoices"] = "Total invoices";
$lang["total_payments"] = "Total payments";
$lang["total_due"] = "Total due";

$lang["show_title"] = "Show title";
$lang["show_border"] = "Show border";

$lang["all_tasks_kanban"] = "All tasks kanban";
$lang["todo_list"] = "Todo list";
$lang["open_projects_list"] = "Open Projects List";
$lang["starred_projects"] = "Starred Projects";
$lang["completed_projects"] = "Completed Projects";

$lang["new_tickets"] = "New Tickets";
$lang["closed_tickets"] = "Closed Tickets";

$lang["clocked_in_team_members"] = "Clocked in team members";
$lang["clocked_out_team_members"] = "Clocked out team members";
$lang["latest_online_client_contacts"] = "Latest online client contacts";
$lang["latest_online_team_members"] = "Latest online team members";
$lang["my_tasks_list"] = "My tasks list";

$lang["discount"] = "Discount";
$lang["discount_type"] = "Discount Type";
$lang["edit_discount"] = "Edit discount";
$lang["discount_amount"] = "Discount amount";
$lang["fixed_amount"] = "Fixed Amount";
$lang["before_tax"] = "Before Tax";
$lang["after_tax"] = "After Tax";

$lang["access_permission"] = "Access Permission";
$lang["setup"] = "Setup";
$lang["client_permissions"] = "Client permissions";

$lang["invoice_over_payment_error_message"] = "You can't pay more than your invoice due.";
$lang["account_already_exists_for_your_company_name"] = "Account already exists for your company name.";
$lang["personal_language"] = "Personal language";
$lang["no_messages_text"] = "You don't have any messages yet";
$lang["no_users_found"] = "No users found";

$lang["create_project"] = "Create project";

/* Version 2.2 */

$lang["imap_settings"] = "IMAP settings";
$lang["enable_email_piping"] = "Enable Email piping";
$lang["imap_host"] = "IMAP Host";
$lang["imap_port"] = "Port";
$lang["imap_ssl_enabled"] = "SSL Enabled";
$lang["please_upgrade_your_php_version"] = "Please upgrade your PHP Version for this operation.";
$lang["required_version"] = "Required Version";
$lang["email_piping_help_message"] = "Please make sure that, your IMap access is enabled.";

$lang["enable_rich_text_editor"] = "Enable rich text editor in comments/description";

$lang["show_assigned_tasks_only"] = "Show assigned tasks only";

$lang["batch_update"] = "Batch update";
$lang["cancel_selection"] = "Cancel selection";
$lang["select_status"] = "Select status";

$lang["add_multiple_tasks"] = "Add multiple tasks";
$lang["save_and_add_more"] = "Save & add more";
$lang["add_project_time"] = "Add project time";
$lang["add_to_do"] = "Add to do";
$lang["hide_menus_from_topbar"] = "Hide menus from topbar";
$lang["favorite_projects"] = "Favorite projects";
$lang["favorite_clients"] = "Favorite clients";
$lang["dashboard_customization"] = "Dashboard customization";
$lang["quick_add"] = "Quick add";

$lang["assign_to_me"] = "Assign to me";

$lang["favicon"] = "Favicon";

$lang["enable_google_drive_api_to_upload_file"] = "Enable Google Drive API to upload file";
$lang["drive_activation_help_message"] = "From now on, all files will be uploaded into Google Drive.";

$lang["mark_all_as_read"] = "Mark all as read";
$lang["marked_all_notifications_as_read"] = "Marked all notifications as read";

$lang["project_completed"] = "Project completed";
$lang["notification_project_completed"] = "Completed a project";

$lang["google_drive_client_id"] = "Client ID";
$lang["google_drive_client_secret"] = "Client secret";
$lang["get_your_app_credentials_from_here"] = "Get your app credentials from here:";
$lang["remember_to_add_this_url_in_authorized_redirect_uri"] = "Remember to add this url in Authorized redirect uri";
$lang["save_and_authorize"] = "Save & authorize";

$lang["preview_next_key"] = "Next (Right arrow key)";
$lang["preview_previous_key"] = "Previous (Left arrow key)";

$lang["filters"] = "Filters";

$lang["authorized"] = "Authorized";
$lang["unauthorized"] = "Unauthorized";

$lang["not_clocked_id_yet"] = "Not clocked in yet";

$lang["create_estimate_request"] = "Create estimate request";

$lang["in_last_number_of_days"] = "In last %s days";
$lang["in_last_number_of_month"] = "In last %s month";
$lang["in_last_number_of_months"] = "In last %s months";

$lang["pusher_app_id"] = "App ID";
$lang["pusher_key"] = "Key";
$lang["pusher_secret"] = "Secret";
$lang["pusher_cluster"] = "Cluster";
$lang["enable_push_notification"] = "Enable push notification";
$lang["push_notification"] = "Push notification";
$lang["disable_push_notification"] = "Disable push notification";

$lang["unknown_client"] = "Unknown client";

$lang["income_expenses_widget_help_message"] = "This report is only usable if you are using single currency.";

$lang["assign_myself_in_this_ticket"] = "Assign myself in this ticket";

$lang["create_new_task"] = "Create new task";

$lang["default_due_date_after_billing_date"] = "Default due date after billing date";

$lang["field_type_external_link"] = "External link";

$lang["total_days"] = "Total days";

$lang["my_timesheet"] = "My timesheet";
$lang["all_timesheets"] = "All timesheets";
$lang["my_timesheet_statistics"] = "My timesheet statistics";
$lang["all_timesheets_statistics"] = "All timesheets statistics";

$lang["no_field_has_selected"] = "No field has selected!";

$lang["imap_help_message_1"] = "You can setup an email address to create the tickets automatically when you receive any emails at that address.";
$lang["imap_help_message_2"] = "Please note that, the system will create tickets based on the unread emails. After creating the ticket, the emails will be marked as read. To get the replies in the same tickets, the system will check the ticket ID in the email subject. If there is no ticket ID in the subject, that will be considered as a new ticket. You can setup the email subject from the";
$lang["imap_error_credentials_message"] = "Error! Can't connect with the imap using the credentials.";

$lang["client_message_own_contacts"] = "Client can send/receive message to/from own contacts?";

$lang["print_invoice"] = "Print invoice";

$lang["mark_invoice_as_cancelled"] = "Mark as cancelled";
$lang["cancelled"] = "Cancelled";
$lang["cancelled_at"] = "Cancelled at";
$lang["cancelled_by"] = "Cancelled by";

/* Version 2.3 */

$lang["test_push_notification"] = "Test push notification";
$lang["notification_test_push_notification"] = "It's a demo push notification";
$lang["push_notification_error_message"] = "Error! Can't connect with the Pusher using the credentials.";
$lang["clone_estimate"] = "Clone Estimate";

$lang["import_clients"] = "Import clients";
$lang["download_sample_file"] = "Download sample file";

$lang["estimate_settings"] = "Estimate Settings";
$lang["estimate_logo"] = "Estimate Logo";
$lang["estimate_color"] = "Estimate Color";
$lang["initial_number_of_the_estimate"] = "Initial number of the estimate";
$lang["the_estimates_id_must_be_larger_then_last_estimate_id"] = "The estimates ID must be larger then last estimate ID.";

$lang["send_to_client"] = "Send to client";
$lang["estimate_sent_message"] = "The estimate has been sent!";
$lang["send_estimate_bcc_to"] = "When sending estimate to client, send BCC to";

$lang["task_settings"] = "Task settings";
$lang["enable_recurring_option_for_tasks"] = "Enable recurring option for tasks";
$lang["past_recurring_date_error_message_title_for_tasks"] = "The selected start date and repeat type returns a past date.";
$lang["recurring_task_created_via_cron_job"] = "Recurring task created via Cron Job";
$lang["notification_recurring_task_created_via_cron_job"] = "New task created";
$lang["repeat_type"] = "Repeat type";
$lang["lead_status"] = "Lead status";
$lang["add_lead_status"] = "Add lead status";
$lang["edit_lead_status"] = "Edit lead status";
$lang["delete_lead_status"] = "Delete lead status";
$lang["owner"] = "Owner";
$lang["make_client"] = "Make client";
$lang["client_contacts"] = "Client contacts";
$lang["lead_contacts"] = "Lead contacts";
$lang["add_a_lead"] = "Add a lead";
$lang["source"] = "Source";
$lang["lead_source"] = "Lead source";
$lang["add_lead_source"] = "Add lead source";
$lang["edit_lead_source"] = "Edit lead source";
$lang["delete_lead_source"] = "Delete lead source";
$lang["custom_field_migration"] = "Custom field migration";
$lang["merge_custom_fields"] = "Merge custom fields";
$lang["do_not_merge"] = "Do not merge";
$lang["merge_custom_fields_help_message"] = "If there is any similar custom fields exists for %s, this values will be added to those. Otherwise, this will create new custom fields for %s and add values to those.";
$lang["lead_created"] = "Lead created";
$lang["notification_lead_created"] = "Created a new lead.";
$lang["client_created_from_lead"] = "Client created from lead";
$lang["notification_client_created_from_lead"] = "Converted a lead to client.";
$lang["project_deadline"] = "Project deadline";
$lang["task_deadline"] = "Task deadline";
$lang["event_type"] = "Event type";
$lang["delete_estimate_form"] = "Delete estimate form";
$lang["calendar_event_modified"] = "Calendar event modified";
$lang["notification_calendar_event_modified"] = "Modified an event.";

$lang["there_has_leads_with_this_status"] = "There has leads with this status";
$lang["lead_created_at"] = "Lead created at";
$lang["past_lead_information"] = "Past lead information";
$lang["last_status"] = "Last status";
$lang["migrated_to_client_at"] = "Migrated to client at";
$lang["edit_estimate_form"] = "Edit estimate form";

$lang["please_upload_a_excel_file"] = "Please upload a excel file.";
$lang["back"] = "Back";

$lang["import_client_error_header"] = "There has an invalid header. The indicated field should be <b>%s</b>.";
$lang["import_client_error_company_name_field_required"] = "Company name field is required.";
$lang["import_client_error_contact_name"] = "Contact first name and last name is both required to add a client contact.";
$lang["import_client_error_contact_email"] = "Contact email is required and should be unique to add a client contact.";
$lang["error"] = "Error";
$lang["contact_first_name"] = "Contact first name";
$lang["contact_last_name"] = "Contact last name";
$lang["contact_email"] = "Contact email";

$lang["clone_invoice"] = "Clone Invoice";
$lang["copy_items"] = "Copy items";
$lang["copy_discount"] = "Copy discount";

$lang["clone_task"] = "Clone task";
$lang["copy_checklist"] = "Copy checklist";

$lang["auto_assign_estimate_request_to"] = "Auto assign estimate request to";

$lang["email_template_variable"] = "Email template variable";
$lang["example_variable_name"] = "Example_variable_name";

$lang["imap_extension_error_help_message"] = "You don't have IMAP extension in your server. Please install the extension for this action.";

$lang["initial_number_of_the_invoice"] = "Initial number of the invoice";
$lang["the_invoices_id_must_be_larger_then_last_invoice_id"] = "The invoices ID must be larger then last invoice ID.";

$lang["client_dashboard_help_message"] = "This will be the default dashboard for all clients. Please note that, the information you're seeing here in the widgets, isn't any actual infromation of clients.";

$lang["send_to_lead"] = "Send to lead";
$lang["lead"] = "Lead";
$lang["leads"] = "Leads";
$lang["add_lead"] = "Add lead";
$lang["edit_lead"] = "Edit lead";
$lang["delete_lead"] = "Delete lead";
$lang["lead_details"] = "Lead details";
$lang["can_access_leads_information"] = "Can access lead's information?";
$lang["lead_info"] = "Lead info";

$lang["send_task_reminder_on_the_day_of_deadline"] = "Send task reminder on the day of deadline";
$lang["send_task_deadline_pre_reminder"] = "Send task deadline pre reminder";
$lang["send_task_deadline_overdue_reminder"] = "Send task deadline overdue reminder";

$lang["project_task_deadline_reminder"] = "Project task deadline reminder";

$lang["project_task_deadline_pre_reminder"] = "Project task deadline pre reminder";
$lang["project_task_deadline_overdue_reminder"] = "Project task deadline overdue reminder";
$lang["project_task_reminder_on_the_day_of_deadline"] = "Project task reminder on the day of deadline";

$lang["notification_project_task_deadline_pre_reminder"] = "Reminder: Some tasks needs to be finished soon.";
$lang["notification_project_task_deadline_overdue_reminder"] = "Reminder: Task's deadline overdue.";
$lang["notification_project_task_reminder_on_the_day_of_deadline"] = "Reminder: Some tasks needs to be finished today.";

$lang["mark_as_public"] = "Mark as public";
$lang["note_details"] = "Note details";
$lang["public_note_by"] = "Public note by";
$lang["marked_as_public"] = "Marked as public";

$lang["client_can_view_activity"] = "Client can view project activity";

$lang["event_settings"] = "Event settings";
$lang["enable_google_calendar_api"] = "Enable Google calendar API";
$lang["google_calendar_settings"] = "Google calendar settings";

$lang["your_calendar_ids"] = "Your Calendar IDs";
$lang["calendar_id"] = "Calendar ID";
$lang["now_every_user_can_integrate_with_their_google_calendar"] = "Now every user can integrate with their Google calendar.";
$lang["calendar_ids_help_message"] = "You'll get your own calendar events always. This is for other special calendars (Like Holidays Calendar).";

$lang["google_client_id"] = "Client ID";
$lang["google_client_secret"] = "Client secret";
$lang["integrate_with_google_calendar"] = "Integrate with Google calendar";
$lang["google_calendar_event"] = "Google Calendar event";

$lang["mark_as_public_help_message"] = "You can't make this note as private again.";

$lang["google_calendar_help_message"] = "You'll get your Google Calendar events by the run of Cron job. And any add/modification of your local events will effect your Google calendar instantly.";

/* Version 2.4 */

$lang["footer"] = "Footer";
$lang["footer_description_message"] = "This footer will be visible on all public pages.";
$lang["estimate_footer"] = "Estimate Footer";
$lang["enable_footer"] = "Enable footer";
$lang["footer_menus"] = "Footer menus";
$lang["footer_copyright_text"] = "Copyright text";
$lang["edit_footer_menu"] = "Edit footer menu";

$lang["menu_name"] = "Menu name";
$lang["task_point_range"] = "Task point range";

$lang["gdpr"] = "GDPR";
$lang["enable_gdpr"] = "Enable GDPR";
$lang["allow_clients_to_export_their_data"] = "Allow clients to export their data";
$lang["export_my_data"] = "Export my data";

$lang["clients_can_request_account_removal"] = "Clients can request account removal";
$lang["i_want_to_remove_my_account"] = "I want to remove my account";
$lang["client_contact_requested_account_removal"] = "Client contact requested account removal";
$lang["notification_client_contact_requested_account_removal"] = "Requested account removal.";
$lang["show_terms_and_conditions_in_client_signup_page"] = "Show Terms and Conditions in client signup page";
$lang["i_accept_the_terms_and_conditions"] = "I accept the";

$lang["apply"] = "Apply";
$lang["applied"] = "Applied";
$lang["export"] = "Export";

$lang["pages"] = "Pages";
$lang["add_page"] = "Add page";
$lang["delete_page"] = "Delete page";
$lang["page_url_cant_duplicate"] = "Page URL can't duplicate.";

$lang["sub_tasks"] = "Sub tasks";
$lang["sub_task"] = "Sub task";
$lang["create_a_sub_task"] = "Create a sub task";
$lang["create"] = "Create";
$lang["parent_task"] = "Parent task";

$lang["this_task_blocked_by"] = "This task blocked by";
$lang["this_task_blocking"] = "This task blocking";
$lang["add_dependency"] = "Add dependency";
$lang["blocked_by"] = "Blocked by";
$lang["blocking"] = "Blocking";
$lang["blocked"] = "Blocked";
$lang["dependency"] = "Dependency";

$lang["estimate_request_settings"] = "Estimate request settings";
$lang["hidden_client_fields_on_public_estimate_requests"] = "Hide fields from public estimate request forms";
$lang["hidden_client_fields"] = "Hidden client fields";

$lang["account"] = "Account";
$lang["common"] = "Common";

$lang["tax_deducted_at_source"] = "TDS";
$lang["auto_close_ticket_after"] = "Auto close ticket after"; //after x days
$lang["disable_user_invitation_option_by_clients"] = "Disable user invitation option by clients";
$lang["create_tickets_only_by_registered_emails"] = "Create tickets only by registered emails";
$lang["icon"] = "Icon";
$lang["help_articles"] = "Help articles";
$lang["help_categories"] = "Help categories";
$lang["knowledge_base_articles"] = "KB articles";
$lang["knowledge_base_categories"] = "KB categories";

$lang["rtl"] = "RTL";

$lang["disable_editing_by_clients"] = "Disable editing by clients";

$lang["client_left_menu"] = "Left menu";
$lang["left_menu_for_client"] = "Left menu for client";
$lang["left_menu_setting_help_message_for_client"] = "This will be the default left menu for clients. Please note that, the menu items will be distributed as per client contact's permissions.";
$lang["available_menu_items"] = "Available menu items";
$lang["drag_and_drop_items_here"] = "Drag and drop items here";
$lang["no_more_items_available"] = "No more items available";
$lang["left_menu_preview_message"] = "Hit save button to see preview.";
$lang["left_menu_setting_help_message"] = "This will be the default left menu for team members. Please note that, the menu items will be distributed as per user's permission.";

$lang["draft_invoices"] = "Draft invoices";
$lang["draft_invoices_total"] = "Draft Invoices Total";
$lang["draft_invoices_value"] = "Draft invoices value";

$lang["gdpr_terms_and_conditions_link"] = "Terms and Conditions URL";
$lang["gdpr_terms_and_conditions"] = "Terms and Conditions";
$lang["removal_request_pending"] = "Removal Request Pending";

$lang["client_access_files_help_message"] = "The files which are located in client details view in the Files tab.";
$lang["estimate_request_name_email_error_message"] = "Email can't be shown without first name and last name.";

$lang["slug"] = "Slug";
$lang["add_assignee"] = "Add assignee";

$lang["client_can_pay_invoice_without_login"] = "Client can pay invoices without login";
$lang["client_can_pay_invoice_without_login_help_message"] = "Please add the PUBLIC_PAY_INVOICE_URL in the invoice email notification template.";

$lang["link_to_existing_client"] = "Link to existing client";
$lang["link_to_new_client"] = "Link to new client";

$lang["client_can_view_files"] = "Client can view files?";
$lang["client_can_add_files"] = "Client can add files?";
$lang["client_can_view_activity"] = "Client can view project activity?";
$lang["client_can_edit_projects"] = "Client can edit projects?";

$lang["view_pdf"] = "View PDF";

$lang["add_new_task"] = "Add new task";
$lang["disable_keyboard_shortcuts"] = "Disable keyboard shortcuts";
$lang["keyboard_shortcuts_info"] = "Keyboard shortcuts info";
$lang["edit_shortcuts"] = "Edit shortcuts";

$lang["pending_leave_approval"] = "Pending leave approval";
$lang["add_attachment"] = "Add Attachment";

$lang["hidden_topbar_menus"] = "Hidden topbar menus";

$lang["make_previous_items_sub_menu"] = "Make/remove sub menu of the previous item";
$lang["add_menu_item"] = "Add menu item";
$lang["url"] = "URL";

$lang["show_theme_color_changer"] = "Show theme color changer";
$lang["default_theme_color"] = "Default theme color";
$lang["left_menu"] = "Left menu";
$lang["client_assigned_contacts"] = "Assigned client contacts";
$lang["timesheet_settings"] = "Timesheet Settings";
$lang["users_can_start_multiple_timers_at_a_time"] = "Users can start multiple timers at a time";

$lang["delete_expenses_category"] = "Delete expenses category";

/* Version 2.5 */

$lang["code_reference"] = "Code Reference";

$lang["commit_url"] = "Commit url";
$lang["new_commits"] = "New commits";
$lang["new_commit"] = "New commit";
$lang["pushed_by"] = "Pushed by";
$lang["committed_by"] = "Committed by";
$lang["add_webhook_in_your_repository_at"] = "Add webhook in your repository: ";
$lang["webhook_listener_link"] = "Webhook listener link";
$lang["enable_bitbucket_commit_logs_in_tasks"] = "Enable bitbucket commit logs in tasks";
$lang["bitbucket_info_text"] = "To link the commits with tasks, there should be a # and task ID at the end of each commit messages. Ex: This is a commit of Task #10.";

$lang["bitbucket_push_received"] = "Bitbucket notification received";
$lang["notification_bitbucket_push_received"] = "Bitbucket notification received.";

$lang["hour_log_time_error_message"] = "Please input hour(s) in correct format.";
$lang["set_message_permissions"] = "Set message permissions";
$lang["cant_send_any_messages"] = "Can't send any messages";
$lang["can_send_messages_to_specific_members_or_teams"] = "Can send messages to specific members or teams:";

$lang["embed"] = "Embed";
$lang["copy"] = "Copy";

$lang["estimate_prefix"] = "Estimate prefix";

$lang["likes"] = "Likes";

$lang["pusher"] = "Pusher";
$lang["enable_chat_via_pusher"] = "Enable chat via pusher";

$lang["tasks_list"] = "Tasks List";
$lang["tasks_kanban"] = "Tasks Kanban";
$lang["set_project_tab_order"] = "Set project tab order";
$lang["project_tab_order"] = "Project tab order";
$lang["project_tab_order_help_message"] = "Please note that, this tabs will show as per user's permissions.";
$lang["project_tab_order_help_message_of_client"] = "Please note that, this tabs will show as per client contact's permissions.";
$lang["client_projects"] = "Projects";

$lang["ticket_assigned"] = "Ticket assigned";
$lang["notification_ticket_assigned"] = "Assigned a ticket to %s";

$lang["disable_access_favorite_project_option_for_clients"] = "Disable access favorite project option for clients";
$lang["disable_editing_left_menu_by_clients"] = "Disable editing left menu by clients";
$lang["disable_topbar_menu_customization"] = "Disable topbar menu customization";
$lang["disable_dashboard_customization_by_clients"] = "Disable dashboard customization by clients";

$lang["task_start_date"] = "Task start date";
$lang["project_start_date"] = "Project start date";
$lang["show_on_kanban_card"] = "Show on kanban card";

$lang["original_expense"] = "Original expense";
$lang["expense_details"] = "Expense details";

$lang["read_only"] = "Read only";

$lang["internal_use_only"] = "Internal use only";
$lang["visible_to_team_members_only"] = "Visible to team members only";
$lang["visible_to_clients_only"] = "Visible to clients only";

$lang["open_in_new_tab"] = "Open in new tab";

$lang["client_can_delete_own_files_in_project"] = "Client can delete own files in project";

$lang["enable_slack"] = "Enable slack";
$lang["get_the_webhook_url_of_your_app_from_here"] = "Get the Webhook URL of your App from here:";
$lang["slack_webhook_url"] = "Webhook URL";
$lang["send_a_test_message"] = "Send a test message";
$lang["notification_test_slack_notification"] = "This is a demo message.";
$lang["slack_notification_error_message"] = "Error! Can't connect with the Slack using the credentials.";
$lang["dont_send_any_project_related_notifications_to_this_channel"] = "Don't send any project related notifications to this channel";
$lang["save_and_send_a_test_message"] = "Save & send a test message";

$lang["copy_sub_tasks"] = "Copy sub tasks";

$lang["can_update_only_assigned_tasks_status"] = "Can update only assigned tasks status";

$lang["import_leads"] = "Import leads";
$lang["import_lead_error_contact_name"] = "Contact first name and last name is both required to add a lead contact.";

$lang["deadline_must_be_equal_or_greater_than_start_date"] = "Deadline must be equal or greater than Start date.";

$lang["enable_github_commit_logs_in_tasks"] = "Enable github commit logs in tasks";
$lang["github_push_received"] = "GitHub notification received";
$lang["notification_github_push_received"] = "GitHub notification received.";

$lang["invalid_calendar_id_error_message"] = "This Calendar ID isn't valid or you don't have permission to access this Calendar";
$lang["total_clients"] = "Total clients";
$lang["total_contacts"] = "Total contacts";

$lang["message_sending_error_message"] = "This user doesn't have permission to send message to you. That's why you also can't send message!";

$lang["days_view"] = "Days view";
$lang["weeks_view"] = "Weeks view";
$lang["months_view"] = "Months view";

$lang["move_all_tasks_to_to_do"] = "Move all tasks to To Do";

$lang["started"] = "Started";

$lang["weekends"] = "Weekends";

$lang["invited_client_contact_signed_up"] = "Invited client contact signed up";
$lang["notification_invited_client_contact_signed_up"] = "Invited client contact signed up.";

$lang["ticket_templates"] = "Ticket templates";
$lang["ticket_template"] = "Ticket template";
$lang["tickets_list"] = "Tickets list";
$lang["add_template"] = "Add template";
$lang["edit_template"] = "Edit template";
$lang["insert_template"] = "Insert template";
$lang["private_template"] = "Private template";

$lang["requested_by"] = "Requested by";

$lang["create_new_projects_automatically_when_estimates_gets_accepted"] = "Create new projects automatically when estimates gets accepted";

$lang["typing"] = "Typing";

$lang["new_client_greetings"] = "New client greetings";

$lang["timeline_post_commented"] = "Timeline post commented";
$lang["post_creator"] = "Post creator";
$lang["notification_timeline_post_commented"] = "Commented on a post.";
$lang["created_a_new_post"] = "Created a new post";
$lang["notification_created_a_new_post"] = "Created a new post.";

$lang["verify_email_before_client_signup"] = "Verify email before client signup";
$lang["input_your_email"] = "Input your email";
$lang["verify_email"] = "Client email verification";
$lang["please_continue_your_signup_process"] = "Please continue your signup process.";
$lang["get_started"] = "Get Started";

$lang["manage_labels"] = "Manage labels";

$lang["timesheet"] = "Timesheet";
$lang["users_can_input_only_total_hours_instead_of_period"] = "Users can input only total hours instead of period";
$lang["timesheet_hour_input_help_message"] = "Ex: 1h 20m";

$lang["template"] = "Template";
$lang["template_details"] = "Template details";

$lang["label_existing_error_message"] = "This label already in use. It can't be deleted.";
